{"name": "landing", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "analyze:build": "ANALYZE=true next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@faker-js/faker": "^8.4.1", "@hookform/resolvers": "^3.3.4", "@next/bundle-analyzer": "^14.2.3", "@planetscale/database": "^1.11.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.4", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-scroll-area": "^1.0.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-form": "^1.1.0", "@tanstack/react-store": "^0.7.0", "@team-plain/typescript-sdk": "^2.19.0", "@vercel/analytics": "^1.5.0", "@vercel/og": "^0.6.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "framer-motion": "11.0.23", "fslightbox-react": "^1.7.6", "fumadocs-core": "^14.7.7", "fumadocs-ui": "14.4.0", "geist": "^1.3.0", "github-slugger": "^2.0.0", "lucide-react": "^0.378.0", "mermaid": "^11.6.0", "nanoid": "^5.0.9", "next": "14.2.26", "next-mdx-remote": "^4.4.1", "prism-react-renderer": "^2.3.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.51.3", "react-markdown": "^8.0.7", "react-syntax-highlighter": "^15.5.0", "rehype-raw": "^7.0.0", "remark-gfm": "^3.0.1", "rss": "^1.2.2", "shiki": "^1.12.0", "tailwind-merge": "^2.2.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.0", "zod": "^3.23.5"}, "devDependencies": {"@content-collections/core": "^0.7.2", "@content-collections/mdx": "^0.1.3", "@content-collections/next": "^0.2.0", "@rive-app/react-canvas-lite": "^4.9.5", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/typography": "^0.5.12", "@types/fslightbox-react": "^1.7.7", "@types/node": "^20.14.9", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.0", "@types/react-syntax-highlighter": "^15.5.13", "@types/rss": "^0.0.32", "autoprefixer": "^10.4.19", "postcss": "^8", "tailwindcss": "^3.4.3", "typescript": "^5.5.3"}}