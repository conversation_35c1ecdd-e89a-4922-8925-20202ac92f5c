<svg width="1400" height="541" viewBox="0 0 1400 541" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g opacity="0.7">
  <rect width="21" height="24" rx="3" transform="matrix(-1 0 0 1 524 82)" fill="white" fill-opacity="0.06"/>
  <rect width="15" height="6" rx="1.5" transform="matrix(-1 0 0 1 521 96)" fill="white" fill-opacity="0.06"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 832 276)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 828 276)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 837 279)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 837 279)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 832 276)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 828 276)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 813 276)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 809 276)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 818 279)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 818 279)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 813 276)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 809 276)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 794 276)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 790 276)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 799 279)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 799 279)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 794 276)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 790 276)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 794 260)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 790 260)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 799 263)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 799 263)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 794 260)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 790 260)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 847 76)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 843 76)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 852 79)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 852 79)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 847 76)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 843 76)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 362 273)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 358 273)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 367 276)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 367 276)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 362 273)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 358 273)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 828 76)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 824 76)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 833 79)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 833 79)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 828 76)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 824 76)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 343 273)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 339 273)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 348 276)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 348 276)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 343 273)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 339 273)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 809 76)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 805 76)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 814 79)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 814 79)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 809 76)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 805 76)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 324 273)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 320 273)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 329 276)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 329 276)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 324 273)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 320 273)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 572 360)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 572 360)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 572 370)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 572 370)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 571 364)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 571 364)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 567 364)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 567 364)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 1055 275)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 1055 275)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 1055 285)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 1055 285)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 1054 279)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 1054 279)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 1050 279)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 1050 279)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 181 409)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 181 409)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 181 419)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 181 419)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 180 413)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 180 413)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 176 413)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 176 413)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 588 360)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 588 360)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 588 370)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 588 370)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 587 364)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 587 364)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 583 364)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 583 364)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 1071 275)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 1071 275)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 1071 285)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 1071 285)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 1070 279)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 1070 279)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 1066 279)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 1066 279)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 197 409)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 197 409)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 197 419)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 197 419)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 196 413)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 196 413)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 192 413)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 192 413)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 604 360)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 604 360)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 604 370)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 604 370)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 603 364)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 603 364)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 599 364)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 599 364)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 1087 275)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 1087 275)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 1087 285)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 1087 285)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 1086 279)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 1086 279)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 1082 279)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 1082 279)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 213 409)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 213 409)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 213 419)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 213 419)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 212 413)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 212 413)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 208 413)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 208 413)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 666 111)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 666 111)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 666 121)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 666 121)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 665 115)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 665 115)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 661 115)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 661 115)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 886 321)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 886 321)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 886 331)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 886 331)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 885 325)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 885 325)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 881 325)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 881 325)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 485 372)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 485 372)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 485 382)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 485 382)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 484 376)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 484 376)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 480 376)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 480 376)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 485 392)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 485 392)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 485 402)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 485 402)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 484 396)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 484 396)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 480 396)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 480 396)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 469 372)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 469 372)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 469 382)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 469 382)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 468 376)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 468 376)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 464 376)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 464 376)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 870 321)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 870 321)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 870 331)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 870 331)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 869 325)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 869 325)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 865 325)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 865 325)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 886 341)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 886 341)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 886 351)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 886 351)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 885 345)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 885 345)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 881 345)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 881 345)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="8" transform="matrix(-1 0 0 1 718 359)" fill="white" fill-opacity="0.04"/>
  <rect x="-0.25" y="0.25" width="7.5" height="7.5" transform="matrix(-1 0 0 1 717.5 359)" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 714 363)" fill="white" fill-opacity="0.08"/>
  <rect x="-0.25" y="0.25" width="15.5" height="15.5" transform="matrix(-1 0 0 1 721.5 355)" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 631 126)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 625 126)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 631 132)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 625 132)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 631 138)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 625 138)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 631 144)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 625 144)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 631 150)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 625 150)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 714 321)" fill="white" fill-opacity="0.08"/>
  <rect x="-0.25" y="0.25" width="15.5" height="15.5" transform="matrix(-1 0 0 1 721.5 313)" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <rect width="8" height="8" transform="matrix(-1 0 0 1 718 317)" fill="white" fill-opacity="0.04"/>
  <rect x="-0.25" y="0.25" width="7.5" height="7.5" transform="matrix(-1 0 0 1 717.5 317)" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 348 371)" fill="white" fill-opacity="0.08"/>
  <rect x="-0.25" y="0.25" width="15.5" height="15.5" transform="matrix(-1 0 0 1 355.5 363)" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <rect width="8" height="8" transform="matrix(-1 0 0 1 352 367)" fill="white" fill-opacity="0.04"/>
  <rect x="-0.25" y="0.25" width="7.5" height="7.5" transform="matrix(-1 0 0 1 351.5 367)" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 711 213)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 706 213)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 701 213)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 696 213)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 691 213)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 686 213)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 524 148)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 519 148)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 514 148)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 509 148)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 504 148)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 499 148)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 414 404)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 409 404)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 404 404)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 399 404)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 394 404)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 389 404)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 313 404)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 308 404)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 303 404)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 298 404)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 293 404)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 288 404)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 414 79)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 409 79)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 404 79)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 399 79)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 394 79)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 389 79)" fill="white" fill-opacity="0.08"/>
  <path d="M675.75 98.25H870.75V20" stroke="white" stroke-opacity="0.06" stroke-width="0.5"/>
  <g clip-path="url(#clip0_1807_1129)">
  <path d="M864 386C858.667 380.667 755.111 277.111 704 226" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M856 386C850.667 380.667 747.111 277.111 696 226" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M848 386C842.667 380.667 739.111 277.111 688 226" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M840 386C834.667 380.667 731.111 277.111 680 226" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M832 386C826.667 380.667 723.111 277.111 672 226" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M824 386C818.667 380.667 715.111 277.111 664 226" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M816 386C810.667 380.667 707.111 277.111 656 226" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M808 386C802.667 380.667 699.111 277.111 648 226" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M800 386C794.667 380.667 691.111 277.111 640 226" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M792 386C786.667 380.667 683.111 277.111 632 226" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M784 386C778.667 380.667 675.111 277.111 624 226" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M776 386C770.667 380.667 667.111 277.111 616 226" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M768 386C762.667 380.667 659.111 277.111 608 226" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M760 386C754.667 380.667 651.111 277.111 600 226" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  </g>
  <g clip-path="url(#clip1_1807_1129)">
  <path d="M613 446C607.667 440.667 504.111 337.111 453 286" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M605 446C599.667 440.667 496.111 337.111 445 286" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M597 446C591.667 440.667 488.111 337.111 437 286" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M589 446C583.667 440.667 480.111 337.111 429 286" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M581 446C575.667 440.667 472.111 337.111 421 286" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M573 446C567.667 440.667 464.111 337.111 413 286" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M565 446C559.667 440.667 456.111 337.111 405 286" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M557 446C551.667 440.667 448.111 337.111 397 286" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M549 446C543.667 440.667 440.111 337.111 389 286" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M541 446C535.667 440.667 432.111 337.111 381 286" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M533 446C527.667 440.667 424.111 337.111 373 286" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M525 446C519.667 440.667 416.111 337.111 365 286" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M517 446C511.667 440.667 408.111 337.111 357 286" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M509 446C503.667 440.667 400.111 337.111 349 286" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  </g>
  <path d="M552 299.25H1118.4C1122.31 299.25 1124.27 299.25 1126.11 298.808C1127.74 298.416 1129.3 297.77 1130.73 296.892C1132.35 295.903 1133.73 294.519 1136.5 291.752L1382 46.25" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <path d="M671 199.25H779.396C783.31 199.25 785.266 199.25 787.108 199.692C788.74 200.084 790.301 200.73 791.733 201.608C793.347 202.597 794.731 203.981 797.498 206.748L890 299.25" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <path d="M552 384.25H888.396C892.31 384.25 894.266 384.25 896.108 383.808C897.74 383.416 899.301 382.77 900.733 381.892C902.347 380.903 903.731 379.519 906.498 376.752L984 299.25" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <path d="M580 165.25H670.75V299" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <path d="M1100.75 299V245.25H940.75V299" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 963 260)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 957 260)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 963 266)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 957 266)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 963 272)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 957 272)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 963 278)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 957 278)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 963 284)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 957 284)" fill="white" fill-opacity="0.08"/>
  <path d="M149 390.25H326.75V540" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <path d="M17 487.25H194.75V540" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <path d="M326.75 540.25H19" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <path d="M670.5 214.25H617" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <rect opacity="0.02" width="49" height="79" rx="0.5" transform="matrix(-1 0 0 1 668 217)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 666 219)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 666 225)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 666 231)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 666 237)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 666 243)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 666 249)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 666 255)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 666 261)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 666 267)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 666 273)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 666 279)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 666 285)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 666 291)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 659 219)" fill="white"/>
  <rect opacity="0.15" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 659 225)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 659 231)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 659 237)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 659 243)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 659 249)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 659 255)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 659 261)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 659 267)" fill="white"/>
  <rect opacity="0.15" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 659 273)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 659 279)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 659 285)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 659 291)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 652 219)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 652 225)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 652 231)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 652 237)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 652 243)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 652 249)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 652 255)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 652 261)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 652 267)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 652 273)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 652 279)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 652 285)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 652 291)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 645 219)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 645 225)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 645 231)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 645 237)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 645 243)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 645 249)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 645 255)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 645 261)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 645 267)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 645 273)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 645 279)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 645 285)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 645 291)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 638 219)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 638 225)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 638 231)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 638 237)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 638 243)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 638 249)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 638 255)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 638 261)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 638 267)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 638 273)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 638 279)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 638 285)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 638 291)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 631 219)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 631 225)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 631 231)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 631 237)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 631 243)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 631 249)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 631 255)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 631 261)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 631 267)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 631 273)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 631 279)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 631 285)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 631 291)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 624 219)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 624 225)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 624 231)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 624 237)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 624 243)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 624 249)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 624 255)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 624 261)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 624 267)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 624 273)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 624 279)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 624 285)" fill="white"/>
  <rect opacity="0.15" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 624 291)" fill="white"/>
  <path d="M616.75 165.5V299" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <path d="M423.75 262.25H551.75V213.5" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <path d="M326.75 390.25H423.75V261.5" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <path d="M551.75 263V416.25H423.75V390" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <path d="M537 70V165" stroke="white" stroke-opacity="0.04" stroke-width="2"/>
  <path d="M645 70V165" stroke="white" stroke-opacity="0.04" stroke-width="2"/>
  <path d="M735 299.5V384" stroke="white" stroke-opacity="0.04" stroke-width="2"/>
  <path d="M693 299.5V384" stroke="white" stroke-opacity="0.04" stroke-width="2"/>
  <path d="M261 390.5V540" stroke="white" stroke-opacity="0.04" stroke-width="2"/>
  <path d="M623 384.5V498" stroke="white" stroke-opacity="0.04" stroke-width="2"/>
  <path d="M580 165.25H455.622C453.652 165.25 452.667 165.25 451.72 165.066C450.881 164.902 450.065 164.632 449.294 164.261C448.425 163.842 447.636 163.253 446.056 162.075L428.897 149.277C427.01 147.869 426.067 147.165 425.386 146.272C424.783 145.48 424.333 144.584 424.059 143.627C423.75 142.548 423.75 141.37 423.75 139.016V70" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <path d="M424 95.25H347.75V0" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <path d="M474.75 165.25V213.25H551.75V165.25" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <rect opacity="0.02" width="70" height="42" rx="0.5" transform="matrix(-1 0 0 1 548 168)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 547 170)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 547 177)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 547 184)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 547 191)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 547 198)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 547 205)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 539 170)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 539 177)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 539 184)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 539 191)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 539 198)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 539 205)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 531 170)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 531 177)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 531 184)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 531 191)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 531 198)" fill="white"/>
  <rect opacity="0.15" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 531 205)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 523 170)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 523 177)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 523 184)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 523 191)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 523 198)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 523 205)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 515 170)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 515 177)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 515 184)" fill="white"/>
  <rect opacity="0.15" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 515 191)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 515 198)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 515 205)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 507 170)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 507 177)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 507 184)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 507 191)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 507 198)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 507 205)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 499 170)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 499 177)" fill="white"/>
  <rect opacity="0.15" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 499 184)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 499 191)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 499 198)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 499 205)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 491 170)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 491 177)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 491 184)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 491 191)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 491 198)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 491 205)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 483 170)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 483 177)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 483 184)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 483 191)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 483 198)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 483 205)" fill="white"/>
  <rect opacity="0.02" width="70" height="42" rx="0.5" transform="matrix(-1 0 0 1 189 493)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 188 495)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 188 502)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 188 509)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 188 516)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 188 523)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 188 530)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 180 495)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 180 502)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 180 509)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 180 516)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 180 523)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 180 530)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 172 495)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 172 502)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 172 509)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 172 516)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 172 523)" fill="white"/>
  <rect opacity="0.15" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 172 530)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 164 495)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 164 502)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 164 509)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 164 516)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 164 523)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 164 530)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 156 495)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 156 502)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 156 509)" fill="white"/>
  <rect opacity="0.15" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 156 516)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 156 523)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 156 530)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 148 495)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 148 502)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 148 509)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 148 516)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 148 523)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 148 530)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 140 495)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 140 502)" fill="white"/>
  <rect opacity="0.15" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 140 509)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 140 516)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 140 523)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 140 530)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 132 495)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 132 502)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 132 509)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 132 516)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 132 523)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 132 530)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 124 495)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 124 502)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 124 509)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 124 516)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 124 523)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 124 530)" fill="white"/>
  <g opacity="0.5">
  <rect width="21" height="24" rx="3" transform="matrix(-1 0 0 1 524 82)" fill="white" fill-opacity="0.06"/>
  <rect width="15" height="6" rx="1.5" transform="matrix(-1 0 0 1 521 96)" fill="white" fill-opacity="0.06"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 832 276)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 828 276)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 837 279)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 837 279)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 832 276)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 828 276)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 813 276)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 809 276)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 818 279)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 818 279)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 813 276)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 809 276)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 794 276)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 790 276)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 799 279)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 799 279)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 794 276)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 790 276)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 794 260)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 790 260)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 799 263)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 799 263)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 794 260)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 790 260)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 847 76)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 843 76)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 852 79)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 852 79)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 847 76)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 843 76)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 362 273)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 358 273)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 367 276)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 367 276)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 362 273)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 358 273)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 828 76)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 824 76)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 833 79)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 833 79)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 828 76)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 824 76)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 343 273)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 339 273)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 348 276)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 348 276)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 343 273)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 339 273)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 809 76)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 805 76)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 814 79)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 814 79)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 809 76)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 805 76)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 324 273)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="8" transform="matrix(-1 0 0 1 320 273)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 329 276)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 329 276)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 324 273)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 320 273)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 572 360)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 572 360)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 572 370)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 572 370)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 571 364)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 571 364)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 567 364)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 567 364)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 1055 275)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 1055 275)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 1055 285)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 1055 285)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 1054 279)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 1054 279)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 1050 279)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 1050 279)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 181 409)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 181 409)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 181 419)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 181 419)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 180 413)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 180 413)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 176 413)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 176 413)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 588 360)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 588 360)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 588 370)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 588 370)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 587 364)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 587 364)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 583 364)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 583 364)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 1071 275)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 1071 275)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 1071 285)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 1071 285)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 1070 279)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 1070 279)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 1066 279)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 1066 279)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 197 409)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 197 409)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 197 419)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 197 419)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 196 413)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 196 413)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 192 413)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 192 413)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 604 360)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 604 360)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 604 370)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 604 370)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 603 364)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 603 364)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 599 364)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 599 364)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 1087 275)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 1087 275)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 1087 285)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 1087 285)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 1086 279)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 1086 279)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 1082 279)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 1082 279)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 213 409)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 213 409)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 213 419)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 213 419)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 212 413)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 212 413)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 208 413)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 208 413)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 666 111)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 666 111)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 666 121)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 666 121)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 665 115)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 665 115)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 661 115)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 661 115)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 886 321)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 886 321)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 886 331)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 886 331)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 885 325)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 885 325)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 881 325)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 881 325)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 485 372)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 485 372)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 485 382)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 485 382)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 484 376)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 484 376)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 480 376)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 480 376)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 485 392)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 485 392)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 485 402)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 485 402)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 484 396)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 484 396)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 480 396)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 480 396)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 469 372)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 469 372)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 469 382)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 469 382)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 468 376)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 468 376)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 464 376)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 464 376)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 870 321)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 870 321)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 870 331)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 870 331)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 869 325)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 869 325)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 865 325)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 865 325)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 886 341)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 886 341)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="2" transform="matrix(-1 0 0 1 886 351)" fill="white" fill-opacity="0.08"/>
  <rect width="8" height="1" transform="matrix(-1 0 0 1 886 351)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 885 345)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 885 345)" fill="white" fill-opacity="0.04"/>
  <rect width="2" height="4" transform="matrix(-1 0 0 1 881 345)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="1" transform="matrix(-1 0 0 1 881 345)" fill="white" fill-opacity="0.04"/>
  <rect width="8" height="8" transform="matrix(-1 0 0 1 718 359)" fill="white" fill-opacity="0.04"/>
  <rect x="-0.25" y="0.25" width="7.5" height="7.5" transform="matrix(-1 0 0 1 717.5 359)" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 714 363)" fill="white" fill-opacity="0.08"/>
  <rect x="-0.25" y="0.25" width="15.5" height="15.5" transform="matrix(-1 0 0 1 721.5 355)" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 631 126)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 625 126)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 631 132)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 625 132)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 631 138)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 625 138)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 631 144)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 625 144)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 631 150)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 625 150)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 714 321)" fill="white" fill-opacity="0.08"/>
  <rect x="-0.25" y="0.25" width="15.5" height="15.5" transform="matrix(-1 0 0 1 721.5 313)" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <rect width="8" height="8" transform="matrix(-1 0 0 1 718 317)" fill="white" fill-opacity="0.04"/>
  <rect x="-0.25" y="0.25" width="7.5" height="7.5" transform="matrix(-1 0 0 1 717.5 317)" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 348 371)" fill="white" fill-opacity="0.08"/>
  <rect x="-0.25" y="0.25" width="15.5" height="15.5" transform="matrix(-1 0 0 1 355.5 363)" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <rect width="8" height="8" transform="matrix(-1 0 0 1 352 367)" fill="white" fill-opacity="0.04"/>
  <rect x="-0.25" y="0.25" width="7.5" height="7.5" transform="matrix(-1 0 0 1 351.5 367)" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 711 213)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 706 213)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 701 213)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 696 213)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 691 213)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 686 213)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 524 148)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 519 148)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 514 148)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 509 148)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 504 148)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 499 148)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 414 404)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 409 404)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 404 404)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 399 404)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 394 404)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 389 404)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 313 404)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 308 404)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 303 404)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 298 404)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 293 404)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 288 404)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 414 79)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 409 79)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 404 79)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 399 79)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 394 79)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="6" transform="matrix(-1 0 0 1 389 79)" fill="white" fill-opacity="0.08"/>
  <path d="M675.75 98.25H870.75V20" stroke="white" stroke-opacity="0.06" stroke-width="0.5"/>
  <g clip-path="url(#clip2_1807_1129)">
  <path d="M864 386C858.667 380.667 755.111 277.111 704 226" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M856 386C850.667 380.667 747.111 277.111 696 226" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M848 386C842.667 380.667 739.111 277.111 688 226" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M840 386C834.667 380.667 731.111 277.111 680 226" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M832 386C826.667 380.667 723.111 277.111 672 226" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M824 386C818.667 380.667 715.111 277.111 664 226" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M816 386C810.667 380.667 707.111 277.111 656 226" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M808 386C802.667 380.667 699.111 277.111 648 226" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M800 386C794.667 380.667 691.111 277.111 640 226" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M792 386C786.667 380.667 683.111 277.111 632 226" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M784 386C778.667 380.667 675.111 277.111 624 226" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M776 386C770.667 380.667 667.111 277.111 616 226" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M768 386C762.667 380.667 659.111 277.111 608 226" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M760 386C754.667 380.667 651.111 277.111 600 226" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  </g>
  <g clip-path="url(#clip3_1807_1129)">
  <path d="M613 446C607.667 440.667 504.111 337.111 453 286" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M605 446C599.667 440.667 496.111 337.111 445 286" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M597 446C591.667 440.667 488.111 337.111 437 286" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M589 446C583.667 440.667 480.111 337.111 429 286" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M581 446C575.667 440.667 472.111 337.111 421 286" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M573 446C567.667 440.667 464.111 337.111 413 286" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M565 446C559.667 440.667 456.111 337.111 405 286" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M557 446C551.667 440.667 448.111 337.111 397 286" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M549 446C543.667 440.667 440.111 337.111 389 286" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M541 446C535.667 440.667 432.111 337.111 381 286" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M533 446C527.667 440.667 424.111 337.111 373 286" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M525 446C519.667 440.667 416.111 337.111 365 286" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M517 446C511.667 440.667 408.111 337.111 357 286" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  <path d="M509 446C503.667 440.667 400.111 337.111 349 286" stroke="white" stroke-opacity="0.1" stroke-width="0.3"/>
  </g>
  <path d="M552 299.25H1118.4C1122.31 299.25 1124.27 299.25 1126.11 298.808C1127.74 298.416 1129.3 297.77 1130.73 296.892C1132.35 295.903 1133.73 294.519 1136.5 291.752L1382 46.25" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <path d="M671 199.25H779.396C783.31 199.25 785.266 199.25 787.108 199.692C788.74 200.084 790.301 200.73 791.733 201.608C793.347 202.597 794.731 203.981 797.498 206.748L890 299.25" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <path d="M552 384.25H888.396C892.31 384.25 894.266 384.25 896.108 383.808C897.74 383.416 899.301 382.77 900.733 381.892C902.347 380.903 903.731 379.519 906.498 376.752L984 299.25" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <path d="M580 165.25H670.75V299" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <path d="M1100.75 299V245.25H940.75V299" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 963 260)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 957 260)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 963 266)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 957 266)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 963 272)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 957 272)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 963 278)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 957 278)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 963 284)" fill="white" fill-opacity="0.08"/>
  <rect width="2" height="2" transform="matrix(-1 0 0 1 957 284)" fill="white" fill-opacity="0.08"/>
  <path d="M149 390.25H326.75V540" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <path d="M17 487.25H194.75V540" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <path d="M326.75 540.25H19" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <path d="M670.5 214.25H617" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <rect opacity="0.02" width="49" height="79" rx="0.5" transform="matrix(-1 0 0 1 668 217)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 666 219)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 666 225)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 666 231)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 666 237)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 666 243)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 666 249)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 666 255)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 666 261)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 666 267)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 666 273)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 666 279)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 666 285)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 666 291)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 659 219)" fill="white"/>
  <rect opacity="0.15" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 659 225)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 659 231)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 659 237)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 659 243)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 659 249)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 659 255)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 659 261)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 659 267)" fill="white"/>
  <rect opacity="0.15" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 659 273)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 659 279)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 659 285)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 659 291)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 652 219)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 652 225)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 652 231)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 652 237)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 652 243)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 652 249)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 652 255)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 652 261)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 652 267)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 652 273)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 652 279)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 652 285)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 652 291)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 645 219)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 645 225)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 645 231)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 645 237)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 645 243)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 645 249)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 645 255)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 645 261)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 645 267)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 645 273)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 645 279)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 645 285)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 645 291)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 638 219)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 638 225)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 638 231)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 638 237)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 638 243)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 638 249)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 638 255)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 638 261)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 638 267)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 638 273)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 638 279)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 638 285)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 638 291)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 631 219)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 631 225)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 631 231)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 631 237)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 631 243)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 631 249)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 631 255)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 631 261)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 631 267)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 631 273)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 631 279)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 631 285)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 631 291)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 624 219)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 624 225)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 624 231)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 624 237)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 624 243)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 624 249)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 624 255)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 624 261)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 624 267)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 624 273)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 624 279)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 624 285)" fill="white"/>
  <rect opacity="0.15" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 624 291)" fill="white"/>
  <path d="M616.75 165.5V299" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <path d="M423.75 262.25H551.75V213.5" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <path d="M326.75 390.25H423.75V261.5" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <path d="M551.75 263V416.25H423.75V390" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <path d="M537 70V165" stroke="white" stroke-opacity="0.04" stroke-width="2"/>
  <path d="M645 70V165" stroke="white" stroke-opacity="0.04" stroke-width="2"/>
  <path d="M735 299.5V384" stroke="white" stroke-opacity="0.04" stroke-width="2"/>
  <path d="M693 299.5V384" stroke="white" stroke-opacity="0.04" stroke-width="2"/>
  <path d="M261 390.5V540" stroke="white" stroke-opacity="0.04" stroke-width="2"/>
  <path d="M623 384.5V498" stroke="white" stroke-opacity="0.04" stroke-width="2"/>
  <path d="M580 165.25H455.622C453.652 165.25 452.667 165.25 451.72 165.066C450.881 164.902 450.065 164.632 449.294 164.261C448.425 163.842 447.636 163.253 446.056 162.075L428.897 149.277C427.01 147.869 426.067 147.165 425.386 146.272C424.783 145.48 424.333 144.584 424.059 143.627C423.75 142.548 423.75 141.37 423.75 139.016V70" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <path d="M424 95.25H347.75V0" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <path d="M474.75 165.25V213.25H551.75V165.25" stroke="white" stroke-opacity="0.08" stroke-width="0.5"/>
  <rect opacity="0.02" width="70" height="42" rx="0.5" transform="matrix(-1 0 0 1 548 168)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 547 170)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 547 177)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 547 184)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 547 191)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 547 198)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 547 205)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 539 170)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 539 177)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 539 184)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 539 191)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 539 198)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 539 205)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 531 170)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 531 177)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 531 184)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 531 191)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 531 198)" fill="white"/>
  <rect opacity="0.15" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 531 205)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 523 170)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 523 177)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 523 184)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 523 191)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 523 198)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 523 205)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 515 170)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 515 177)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 515 184)" fill="white"/>
  <rect opacity="0.15" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 515 191)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 515 198)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 515 205)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 507 170)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 507 177)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 507 184)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 507 191)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 507 198)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 507 205)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 499 170)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 499 177)" fill="white"/>
  <rect opacity="0.15" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 499 184)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 499 191)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 499 198)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 499 205)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 491 170)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 491 177)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 491 184)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 491 191)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 491 198)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 491 205)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 483 170)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 483 177)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 483 184)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 483 191)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 483 198)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 483 205)" fill="white"/>
  <rect opacity="0.02" width="70" height="42" rx="0.5" transform="matrix(-1 0 0 1 189 493)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 188 495)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 188 502)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 188 509)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 188 516)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 188 523)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 188 530)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 180 495)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 180 502)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 180 509)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 180 516)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 180 523)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 180 530)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 172 495)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 172 502)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 172 509)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 172 516)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 172 523)" fill="white"/>
  <rect opacity="0.15" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 172 530)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 164 495)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 164 502)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 164 509)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 164 516)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 164 523)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 164 530)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 156 495)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 156 502)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 156 509)" fill="white"/>
  <rect opacity="0.15" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 156 516)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 156 523)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 156 530)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 148 495)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 148 502)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 148 509)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 148 516)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 148 523)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 148 530)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 140 495)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 140 502)" fill="white"/>
  <rect opacity="0.15" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 140 509)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 140 516)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 140 523)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 140 530)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 132 495)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 132 502)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 132 509)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 132 516)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 132 523)" fill="white"/>
  <rect opacity="0.1" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 132 530)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 124 495)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 124 502)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 124 509)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 124 516)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 124 523)" fill="white"/>
  <rect opacity="0.05" width="3" height="3" rx="0.5" transform="matrix(-1 0 0 1 124 530)" fill="white"/>
  </g>
  </g>
  <defs>
  <clipPath id="clip0_1807_1129">
  <rect width="40" height="77" fill="white" transform="matrix(-1 0 0 1 790 303)"/>
  </clipPath>
  <clipPath id="clip1_1807_1129">
  <rect width="80" height="40" fill="white" transform="matrix(-1 0 0 1 539 363)"/>
  </clipPath>
  <clipPath id="clip2_1807_1129">
  <rect width="40" height="77" fill="white" transform="matrix(-1 0 0 1 790 303)"/>
  </clipPath>
  <clipPath id="clip3_1807_1129">
  <rect width="80" height="40" fill="white" transform="matrix(-1 0 0 1 539 363)"/>
  </clipPath>
  </defs>
  </svg>