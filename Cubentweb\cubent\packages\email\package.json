{"name": "@repo/email", "version": "0.0.0", "private": true, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@react-email/components": "0.0.41", "@t3-oss/env-nextjs": "^0.13.4", "react": "^19.1.0", "react-dom": "^19.1.0", "resend": "^4.5.1", "zod": "^3.25.28"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "22.15.21", "@types/react": "19.1.5", "@types/react-dom": "^19.1.5", "typescript": "^5.8.3"}}