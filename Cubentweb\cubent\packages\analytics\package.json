{"name": "@repo/analytics", "version": "0.0.0", "private": true, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@next/third-parties": "15.3.2", "@t3-oss/env-nextjs": "^0.13.4", "@vercel/analytics": "^1.5.0", "posthog-js": "^1.246.0", "posthog-node": "^4.17.2", "react": "^19.1.0", "server-only": "^0.0.1", "zod": "^3.25.28"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "22.15.21", "@types/react": "19.1.5", "@types/react-dom": "^19.1.5"}}