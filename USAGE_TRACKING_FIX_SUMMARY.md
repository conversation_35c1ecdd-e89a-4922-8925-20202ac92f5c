# Cubent Usage Tracking - Server-Based Implementation

## Problem Identified

The Cubent Units Usage was showing 0 even though messages were being sent, and more importantly, **usage data was only stored locally** which meant it would be lost when uninstalling VS Code or clearing cache.

### Root Cause

1. **Storage Key Mismatch**: UsageSettings.tsx was looking for usage data using the old storage key `"cubent-usage-stats"` while usage-tracking.ts was storing data using user-specific keys
2. **Local-Only Storage**: Usage data was only stored in browser localStorage, not synced to the server database
3. **No Persistence**: When users reinstalled VS Code or cleared cache, all usage history was lost

### User Requirements

The user specifically requested that usage data should be:
- **Stored in the database** (Neon/Supabase) rather than locally
- **User-based and persistent** across different installations
- **Synced across devices** when the user logs in

## Solution: Server-First Usage Tracking

The solution implements a **server-first approach** with localStorage as fallback:

1. **Primary Data Source**: Server database (Neon/Supabase)
2. **Fallback**: Local storage for offline scenarios
3. **Caching**: Server data is cached locally for performance
4. **Real-time Sync**: Usage data is sent to server immediately when tracked

## Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Extension     │    │    Webview UI    │    │   Server API    │
│                 │    │                  │    │                 │
│ Track Usage ────┼───▶│ Send to Server ──┼───▶│ Store in DB     │
│                 │    │                  │    │                 │
│ Request Stats ──┼───▶│ Fetch from Server│◄───┤ Return Stats    │
│                 │    │ (fallback: local)│    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Files Modified

### 1. **Server-Side API Enhancement**
`Cubentweb/cubent/apps/app/app/api/extension/usage/stats/route.ts`
- Enhanced to support time period filtering (`?days=N`)
- Returns detailed usage breakdown by model
- Includes entries array for compatibility with frontend
- Provides model-specific usage statistics

### 2. **Extension Message Handler**
`Cubent.dev/src/core/webview/webviewMessageHandler.ts`
- Updated `getServerUsageStats` handler to support async responses
- Added support for time period filtering
- Enhanced error handling and response structure
- Added `messageId` for request/response correlation

### 3. **WebView Message Interface**
`Cubent.dev/src/shared/WebviewMessage.ts`
- Added `messageId?: string` for async message correlation
- Added `days?: number` for time period filtering

### 4. **Usage Tracking Utilities**
`Cubent.dev/webview-ui/src/utils/usage-tracking.ts`
- Added `getUsageStatsAsync()` - server-first with localStorage fallback
- Added `getUsageStatsForPeriodAsync()` - server-first period filtering
- Added `fetchServerUsageStats()` - communicates with extension for server data
- Enhanced error handling and fallback mechanisms

### 5. **Usage Settings Component**
`Cubent.dev/webview-ui/src/components/settings/UsageSettings.tsx`
- Updated to use async server-first functions
- Enhanced error handling with fallback to localStorage
- Improved real-time data refresh capabilities

## How the Server-First System Works

### 1. **Data Flow**
```
User Action → Track Usage → Send to Server → Store in Database
                    ↓
            Cache Locally ← Fetch from Server ← Display in UI
```

### 2. **Authentication & User Context**
- Uses Clerk/Auth0 for user authentication
- Server identifies users by session token
- Anonymous usage is handled gracefully
- User-specific data isolation in database

### 3. **Database Storage**
- **usageMetrics table**: Aggregated daily usage per user
- **usageAnalytics table**: Detailed per-request analytics
- **User table**: User limits and subscription info
- Proper indexing for efficient queries

### 4. **Fallback Mechanism**
- **Primary**: Fetch from server API
- **Secondary**: Read from localStorage cache
- **Tertiary**: Return empty stats with graceful degradation

## Storage Data Structure

```json
{
  "totalCubentUnits": 2.5,
  "totalMessages": 3,
  "entries": [
    {
      "timestamp": *************,
      "modelId": "claude-3-5-sonnet-********",
      "cubentUnits": 0.95,
      "messageCount": 1,
      "provider": "anthropic",
      "configName": "default"
    }
  ],
  "lastUpdated": *************
}
```

## Testing

Created `test-usage-tracking.html` to verify the fix:
- Tests user-specific storage key generation
- Tests usage tracking functionality
- Tests data retrieval and display
- Includes debug functions to inspect localStorage keys

## Key Benefits

### 1. **True User-Based Tracking**
- Usage data tied to user account, not device
- Survives VS Code reinstallation and cache clearing
- Consistent across multiple devices/installations

### 2. **Server-Side Persistence**
- Data stored in Neon/Supabase database
- Proper backup and recovery capabilities
- Scalable for enterprise usage

### 3. **Enhanced Analytics**
- Detailed per-model usage breakdown
- Time-based filtering (last 7 days, 30 days, etc.)
- Historical usage trends

### 4. **Improved Performance**
- Server data cached locally for fast access
- Async loading with graceful fallbacks
- Real-time updates without blocking UI

## Expected Behavior After Implementation

### ✅ **Immediate Benefits**
1. **Usage Settings Page**: Displays correct server-based statistics
2. **Real-time Updates**: Stats update immediately after sending messages
3. **Cross-Device Sync**: Same usage data across all user's devices
4. **Persistent Storage**: Data survives reinstallation and cache clearing

### ✅ **Long-term Benefits**
1. **Historical Analytics**: Track usage trends over time
2. **Subscription Management**: Accurate billing based on server data
3. **Usage Alerts**: Server-side monitoring and notifications
4. **Enterprise Features**: Team usage tracking and management

## Testing & Verification

### 1. **Basic Functionality Test**
```bash
1. Install the updated extension
2. Sign in to Cubent account
3. Send messages using different AI models
4. Check Settings > Usage Settings
5. Verify usage data appears correctly
```

### 2. **Persistence Test**
```bash
1. Note current usage statistics
2. Uninstall VS Code extension
3. Clear browser cache/localStorage
4. Reinstall extension and sign in
5. Verify usage data is restored from server
```

### 3. **Cross-Device Test**
```bash
1. Use extension on Device A, send messages
2. Install extension on Device B
3. Sign in with same account
4. Verify usage data syncs correctly
```

## Migration Strategy

### **Existing Users**
- Local usage data will be preserved as fallback
- Server data takes precedence once available
- Gradual migration as users use the extension

### **New Users**
- All usage data stored server-side from day one
- No dependency on local storage
- Immediate cross-device synchronization

## Technical Notes

- **Backward Compatibility**: Maintains support for offline usage
- **Error Handling**: Graceful degradation when server unavailable
- **Performance**: Async operations don't block UI
- **Security**: All server requests authenticated with session tokens
