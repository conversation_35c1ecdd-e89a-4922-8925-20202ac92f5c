{"name": "@repo/cms", "version": "0.0.0", "private": true, "scripts": {"dev": "basehub dev", "build": "basehub build", "analyze": "basehub", "clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@t3-oss/env-nextjs": "^0.13.4", "basehub": "^8.2.7", "react": "^19.1.0", "zod": "^3.25.28"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "22.15.21", "@types/react": "19.1.5", "@types/react-dom": "^19.1.5", "next": "15.3.2"}}