# 🔍 Usage Tracking Debug Guide

You mentioned that usage data appears in the extension but not in the Neon database. Let's systematically debug this issue.

## Step 1: Check Extension Console for Errors

1. **Open VS Code Developer Tools**:
   - Help → Toggle Developer Tools
   - Go to the Console tab

2. **Look for these specific error messages**:
   ```
   "CloudService not available for server usage tracking"
   "User not authenticated for server usage tracking" 
   "No session token available for server usage tracking"
   "Server usage tracking failed:"
   "Error tracking usage on server:"
   ```

3. **If you see any of these errors**, note the exact message and continue to the relevant section below.

## Step 2: Verify Authentication Status

**In the VS Code Developer Console, run these commands:**

```javascript
// Check if CloudService is available
CloudService.hasInstance()

// Check authentication status
CloudService.instance.isAuthenticated()

// Check if there's an active session
CloudService.instance.hasActiveSession()

// Get session token (should return a long JWT string)
CloudService.instance.getSessionToken()

// Get user info
CloudService.instance.getUserInfo()
```

**Expected Results:**
- `hasInstance()` → `true`
- `isAuthenticated()` → `true` 
- `hasActiveSession()` → `true`
- `getSessionToken()` → Long JWT string (starts with "eyJ...")
- `getUserInfo()` → Object with user details

## Step 3: Test Usage Tracking Manually

**Send a test usage tracking message:**

```javascript
vscode.postMessage({
  type: "trackUserUsage",
  data: {
    modelId: "debug-test-model",
    provider: "debug-provider", 
    configName: "debug-config",
    cubentUnits: 0.5,
    messageCount: 1,
    timestamp: Date.now()
  }
})
```

**Watch the console for:**
- `"🎯 EXTENSION RECEIVED trackUserUsage:"` - Extension received the message
- `"Server usage tracking successful:"` - API call succeeded
- `"Server usage tracking failed:"` - API call failed (note the error details)

## Step 4: Check API Endpoint

**Verify the API URL being used:**

```javascript
// This should show the API base URL (likely https://app.cubent.dev)
console.log("API URL:", CloudService.instance.authService.getRooCodeApiUrl())
```

## Step 5: Common Issues and Solutions

### Issue A: "CloudService not available"
**Cause**: Extension not properly initialized
**Solution**: 
1. Restart VS Code
2. Make sure Cubent extension is enabled
3. Try logging out and back in

### Issue B: "User not authenticated" 
**Cause**: Not logged into the extension
**Solution**:
1. Run the login command: `Cubent: Sign In`
2. Complete the authentication flow
3. Verify authentication with Step 2 commands

### Issue C: "No session token available"
**Cause**: Token expired or authentication failed
**Solution**:
1. Log out: `Cubent: Sign Out`
2. Log back in: `Cubent: Sign In` 
3. Verify new token with `CloudService.instance.getSessionToken()`

### Issue D: "Server usage tracking failed" with 401 Unauthorized
**Cause**: Invalid or expired session token
**Solution**: Same as Issue C - re-authenticate

### Issue E: "Server usage tracking failed" with 404 User not found
**Cause**: User exists in Clerk but not in your database
**Solution**: 
1. Check your database for the user record
2. The user should exist in the `User` table with the correct `clerkId`
3. If missing, there might be an issue with the user creation flow

### Issue F: Network errors (ECONNREFUSED, timeout, etc.)
**Cause**: API server not accessible
**Solution**:
1. Check if your server is running
2. Verify the API URL is correct
3. Check firewall/network settings

## Step 6: Database Verification

**Connect to your Neon database and run these queries:**

```sql
-- Check if your user exists (replace with your actual Clerk user ID)
SELECT * FROM "User" WHERE "clerkId" = 'your-clerk-user-id-here';

-- Check recent usage metrics
SELECT * FROM "UsageMetrics" 
WHERE "userId" = (SELECT id FROM "User" WHERE "clerkId" = 'your-clerk-user-id-here')
ORDER BY "date" DESC 
LIMIT 10;

-- Check recent usage analytics  
SELECT * FROM "UsageAnalytics"
WHERE "userId" = (SELECT id FROM "User" WHERE "clerkId" = 'your-clerk-user-id-here')
ORDER BY "createdAt" DESC
LIMIT 10;
```

## Step 7: Server Logs

**If you have access to server logs, look for:**
- `"Extension usage tracking - Direct Clerk JWT validation successful"`
- `"Extension usage tracking - PendingLogin validation successful"`
- `"Extension usage tracking successful"`
- Any error messages related to usage tracking

## Step 8: Test Server API Directly

**If all else fails, test the API endpoint directly:**

1. Get your session token from Step 2
2. Use curl or Postman to test:

```bash
curl -X POST https://app.cubent.dev/api/extension/usage \
  -H "Authorization: Bearer YOUR_SESSION_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "modelId": "direct-test",
    "provider": "test-provider",
    "configName": "test-config", 
    "cubentUnits": 1,
    "messageCount": 1,
    "timestamp": 1640995200000
  }'
```

## Next Steps

After running through these steps, you should have identified the specific issue. Common outcomes:

1. **Authentication Issue**: Re-authenticate and the problem should resolve
2. **User Record Missing**: Check your user creation flow in the signup process
3. **API Server Issue**: Check server status and logs
4. **Network Issue**: Check connectivity and firewall settings

If you're still having issues after this diagnostic, please share:
1. The exact error messages from the console
2. Your authentication status results from Step 2
3. Any relevant server logs
4. The results of the database queries from Step 6

This will help pinpoint the exact cause of the disconnect between your extension usage data and the Neon database.
