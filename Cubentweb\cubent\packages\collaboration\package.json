{"name": "@repo/collaboration", "version": "0.0.0", "private": true, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@t3-oss/env-nextjs": "^0.13.4", "@liveblocks/client": "^2.24.2", "@liveblocks/node": "^2.24.2", "@liveblocks/react": "^2.24.2", "react": "^19.1.0", "server-only": "^0.0.1", "zod": "^3.25.28"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@types/node": "22.15.21", "@types/react": "19.1.5", "@types/react-dom": "^19.1.5"}}